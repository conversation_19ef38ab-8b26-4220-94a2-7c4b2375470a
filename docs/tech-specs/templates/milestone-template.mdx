---
title: Miles<PERSON> <ID> — <One-line scope>
description: <Short paragraph of intent>
created: <YYYY-MM-DD>
version: 0.0.0
status: Draft
tags: [milestone]
authors: []
---

import { Callout } from '@/components/Callout'

<Callout emoji="🚧">
<strong>Draft.</strong> Replace placeholders before PR.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
# pin exact versions or "inherit-from-root"
```

---

## 🎯 Definition of Done

<text>

---

## 📦 Deliverables

| Path | Must contain … |
|------|----------------|
|      |                |

---

## 🗂 Directory / API Diagram

```text
# Tree or sequence diagram as needed
```

---

## 🧠 Key Decisions

| Topic  | Decision | Rationale |
|--------|----------|-----------|
|        |          |           |

---

## ✅ Success Criteria

- SC-1
- SC-2

---

## 🔨 Task Breakdown

| #  | Branch name | Checklist item |
|----|-------------|---------------|
|    |             |               |

---

## 🤖 CI Pipeline (ready-to-copy)

```yaml
# Workflow snippet
```

---

## 🧪 Acceptance Tests

<text>

<Callout emoji="📝">
Must pass <Link href="../spec-checklist.mdx">spec-checklist</Link> & dry-run before moving to <code>status: Approved</code>.
</Callout>