---
title: Repository Structure & Conventions
description: Living guideline—update with every structural PR.
created: 2025-05-25
updated: 2025-05-25
version: 0.1.0
status: Living
tags: [structure]
authors: [nitishMehrotra]
---

## 📁 Top-Level Folders

| Folder                | Purpose                                         |
|----------------------|-------------------------------------------------|
| `apps/`              | Deployable applications (`api`, `web`).         |
| `packages/`          | Re-usable libs: `shared`, `parser-lib`, `agent-core`, … |
| `docs/tech-spec/`    | Milestone & domain specs (this folder).         |
| `.github/`           | Workflow files, CODEOWNERS.                     |
| `docker-compose.yml` | Local dev stack (api + neo4j).                  |

---

## 🧩 Import & Code Rules

- **Named exports only** — avoids default export confusion.
- **No wildcard re-exports** (`export *`) — use explicit barrel files.
- Shared discriminated unions (`Result`, `Maybe`) live in `packages/shared`.

---

## 🔧 Build & Lint Stack

| Concern     | Tool                        | Note                              |
|-------------|-----------------------------|-----------------------------------|
| Build libs  | **tsup**                    | ESM outDir `/dist`                |
| Build api   | **tsup**                    | Command in `apps/api/package.json` |
| Build web   | **Vite**                    | `vite build` in `apps/web`        |
| Tests       | Jest (api) · Vitest (libs/web) | Fast & familiar                |
| Linting     | ESLint + `@typescript-eslint` | Enforced in CI                  |
| Formatting  | Prettier via ESLint         | Single source of truth            |

---

## 🧪 Testing Layers

| Layer           | Framework         | Location                    |
|-----------------|------------------|-----------------------------|
| Unit (api)      | Jest + Supertest | `apps/api/src/__tests__`    |
| Unit (libs, web)| Vitest           | Co-located `__tests__`      |
| E2E (future)    | Playwright       | `/e2e` root folder          |

---

## 🌳 Branching & Release Flow

| Branch        | Rule                                              |
|---------------|--------------------------------------------------|
| `main`        | Always green; merges via squash-and-merge.        |
| `feat/*`      | Feature work; PR targets `main`.                  |
| `release/*`   | Tag-prep; bump version, update CHANGELOG.         |
| Tags          | `vX.Y.Z` when a milestone spec passes all acceptance tests. |

---

## 🛂 Code Ownership

```text
apps/api/**                 @backend-lead
apps/web/**                 @frontend-lead
packages/**                 @backend-lead
docs/tech-specs/**          @engineering-manager
docs/product-requirements/** @product-manager
```

---

## 🛠️ Spec Governance

| Rule                     | Detail                                                                                                  |
| ------------------------ | ------------------------------------------------------------------------------------------------------- |
| **Spec template**        | Start every milestone from [`template/milestone-template.mdx`](./template/milestone-template.mdx).      |
| **Checklist validation** | Run `node scripts/spec-lint.mjs <spec>` — must exit 0 before PR.                                        |
| **Dry-run gate**         | After a spec is set to **Approved**, run `pnpm run agent:dry-run --spec <file>` before tagging release. |
| **Reviews required**     | At least 3 reviewers: backend, frontend, PM.                                                            |
| **Versioning**           | Bump `version:` here on any structural-rule change and add an entry to `decision-log.mdx`.              |

<Callout emoji="🪄">
Spec-driven scaffolding helpers live in <code>scripts/</code>. <code>spec-lint.mjs</code> is the first; add more per milestone.
</Callout>

🗒️ **Rule Versioning**
When you change any structural rule, increment the version in the front-matter of this file and append a note to `decision-log.mdx`.

<Callout emoji="✏️">
Planning to reorganise folders? Open a PR that edits this file *first*, then update code. Docs ahead of code = fewer review surprises.
</Callout>



