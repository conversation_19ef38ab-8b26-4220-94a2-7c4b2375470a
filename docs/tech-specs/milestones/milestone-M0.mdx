---
title: Milestone M0 — Repository Skeleton & CI
description: The contractual scope, decisions, and acceptance tests for the very first deliverable.
created: 2025-05-25
updated: 2025-05-25
version: 0.2.0
status: Approved
tags: [milestone]
authors: [nitishMehrotra]
---

import { Callout } from '@/components/Callout'

<Callout emoji="🚀">
<strong>M0 is *all infrastructure*.</strong><br />
No business logic, no parsing—just a rock-solid monorepo skeleton that builds, tests, lints, containers, and passes CI.
</Callout>

---

## 🎯 Goal (“Definition of Done”)

1. Fresh clone → `pnpm install && pnpm lint && pnpm test && pnpm type-check` completes **≤ 60 s** with exit code 0.
2. Pre-commit hooks (Husky + lint-staged) run lint, format, and test on staged files before commit.
3. GitHub Actions workflow (`ci.yml`) runs lint, type-check, test, coverage, and audit on **every push & PR** and is green on `main`.
4. `docker compose up` starts **api** (`:3000`), **web** (`:5173`), & **neo4j** (`:7474`). Both `curl http://localhost:3000/health` and `curl http://localhost:5173/health` return **200 OK**.
5. API can run in mock DB mode for local dev (documented in README).
6. Root `README.md` and `CONTRIBUTING.md` document all scripts, setup, troubleshooting, and contribution guidelines.
7. `CHANGELOG.md` and `SECURITY.md` exist and are referenced in the README.
8. This spec remains in `docs/tech-spec/milestones/` and is referenced from the CHANGELOG entry for tag `v0.2.0`.
9. Tool versions (Node, pnpm, Docker) are pinned and documented.
10. Test coverage tooling is set up and reported in CI (threshold can be relaxed for M0, but must be present).
11. GitHub Actions workflow (ci.yml) green on push & PR.
12. Agent PRs follow naming convention M0-Task-<##>: <desc> + label auto-agent.

---

## 📦 Deliverables & File Stubs

| Path                        | Must exist with content                                 |
|-----------------------------|--------------------------------------------------------|
| pnpm-workspace.yaml         | lists apps/* & packages/*.                              |
| package.json                | root scripts (see Scripts section).                     |
| apps/api/src/index.ts       | minimal Express server + /health.                       |
| apps/web/                   | Vite React starter (main.tsx, App.tsx).                 |
| packages/shared/Result.ts   | discriminated union helper.                             |
| Dockerfile & docker-compose.yml | see stubs below.                                   |
| .github/workflows/ci.yml    | exactly as provided.                                    |
| scripts/m0-acceptance.sh    | acceptance test script (below).                         |
| tsconfig.json, .eslintrc.cjs, .prettierrc, jest.config.ts, vitest.config.ts | use stubs. |
| This spec file.             |                                                        |

---

## 🧳 Toolchain Versions

```yaml
node: "20.11.0"
pnpm: "8.15.4"
typescript: "5.4.3"
tsup: "7.2.0"
turbo: "latest"
vite: "5.2.2"
jest: "29.7.0"
vitest: "1.5.0"
express: "5.0.0-beta.1"
supertest: "6.4.2"
eslint: "8.56.0"
prettier: "3.2.5"
gray-matter: "4.1.0"   # for spec-lint
```

---

## 🗂 Directory Layout (after M0)

```text
repo-root/
├─ apps/
│  ├─ api/            # Express server (with mock DB mode)
│  └─ web/            # React UI (with health endpoint)
├─ packages/
│  ├─ shared/         # Re-usable types & configs
│  └─ (future libs)/
├─ docs/
│  └─ tech-spec/
│     ├─ milestones/
│     │  └─ milestone-M0.mdx
│     ├─ 00_structure.mdx
│     └─ domains/     # added in later milestones
├─ .github/workflows/ci.yml
├─ .husky/                    # Pre-commit hooks
├─ docker-compose.yml
├─ README.md
├─ CONTRIBUTING.md
├─ CHANGELOG.md
├─ SECURITY.md
└─ pnpm-workspace.yaml
```

---

## 🧠 Key Decisions

| Topic           | Decision                                       | Rationale                         |
| --------------- | ---------------------------------------------- | --------------------------------- |
| Package manager | **pnpm 8**                                     | Fast, workspace-native installs   |
| Monorepo runner | **Turborepo** (can swap for Nx later)          | Incremental builds & caching      |
| Backend build   | **tsup** emits ESM                             | No TS runtime in prod image       |
| API tests       | **Jest + Supertest**                           | Battle-tested for HTTP assertions |
| UI tests        | **Vitest**                                     | Shares Vite pipeline; fast HMR    |
| Lint/Format     | ESLint + Prettier via `eslint-plugin-prettier` | Single opinionated style          |
| Containers      | `node:20-slim` + `neo4j:5`                     | Small images, easy orchestration  |

A full architectural record goes to `docs/tech-spec/decision-log.mdx` (created in milestone M1).

---

## ✅ Success Criteria (repeatable checklist)

- [ ] SC-1 All commands in Goal section complete successfully on macOS, Linux, and CI.
- [ ] SC-2 CI badge in README.md shows passing status on main.
- [ ] SC-3 `docker compose ps` shows both services healthy.
- [ ] SC-4 All tables in this doc match the real folder tree (reviewer tick-off).
- [ ] SC-5 Spec passes checklist lint:

  ```sh
  node scripts/spec-lint.mjs docs/tech-spec/milestones/milestone-M0.mdx
  # (exit 0 required)
  ```

- [ ] SC-6 Agent dry-run passes:

  ```sh
  pnpm run agent:dry-run --spec docs/tech-spec/milestones/milestone-M0.mdx
  ```

---

## 🔨 Task Breakdown (issue-sized)

| #  | Branch name          | Task (issue title)                                   | Owner   | Acceptance hint                       |
|----|----------------------|------------------------------------------------------|---------|---------------------------------------|
| 01 | `m0/init-repo`       | Initialise empty Git repo on GitHub                  | Lead    | `main` branch created                 |
| 02 | `m0/workspace-file`  | Add `pnpm-workspace.yaml`, run `pnpm init`           | BE      | `pnpm i` zero warnings                |
| 03 | `m0/scaffold-api`    | Scaffold **api** app (`express`, `tsup`)             | BE      | `pnpm dev:api` prints "listening"     |
| 04 | `m0/scaffold-web`    | Scaffold **web** app (Vite React)                    | FE      | `pnpm dev:web` opens on 5173          |
| 05 | `m0/shared-lib`      | Create **shared** package & export `Result` type     | BE      | Imported from api & web               |
| 06 | `m0/lint-config`     | Configure ESLint & Prettier root configs             | FE      | `pnpm lint` exits 0                   |
| 07 | `m0/sample-tests`    | Add sample unit tests (Jest, Vitest)                 | BE & FE | CI shows tests passing                |
| 08 | `m0/ci-pipeline`     | Author GitHub Actions `ci.yml`                       | DevOps  | Green check on PR                     |
| 09 | `m0/docker-stack`    | Author `Dockerfile` & `docker-compose.yml`           | DevOps  | `curl /health` → 200                  |
| 10 | `m0/acceptance`      | Add `scripts/m0-acceptance.sh` + README badge        | DevOps  | Script exits 0 locally                |
| 11 | `m0/spec-quality`    | Ensure spec passes `spec-lint` & mark **Approved**   | PM      | `node scripts/spec-lint.mjs …` exit 0 |
| 12 | `m0/final-tag`       | Merge all PRs, tag `v0.0.1`, close milestone         | Lead    | CI green on `main`, tag pushed        |

<Callout emoji="🗂">
Open **one PR per row**. Reviewers tick the checkbox in the PR description when the
Acceptance hint is satisfied.
</Callout>

---

## CI Pipeline (skim)

```yaml
name: CI
on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - uses: actions/setup-node@v4
        with: { node-version: 20.11.0, cache: pnpm }
      - run: corepack enable
      - run: pnpm install --frozen-lockfile
      - run: pnpm lint
      - run: pnpm test --recursive
      - run: pnpm build
      - run: bash scripts/m0-acceptance.sh
```

Later milestones will extend this file—not replace it.

---

## 🧪 Acceptance Tests

### 1️⃣ Clean-room build

```bash
git clone <repo> && cd <repo>
pnpm install && pnpm build
# Expected: 0 warnings, 0 errors
```

### 2️⃣ Unit-test & lint suite

```bash
pnpm lint            # exit 0
pnpm test --recursive
# Expected: Jest & Vitest run ≥ 1 spec each, all green
```

### 3️⃣ Container health

```bash
docker compose up -d --wait
curl -fs http://localhost:3000/health | grep '"status":"ok"'
# Expected: HTTP 200 with JSON { "status": "ok" }
```

### 4️⃣ Spec checklist lint

```bash
node scripts/spec-lint.mjs docs/tech-spec/milestones/milestone-M0.mdx
# Expected: "✅  Spec passed lint"
```

### 5️⃣ Agent dry-run

```bash
pnpm run agent:dry-run --spec docs/tech-spec/milestones/milestone-M0.mdx
# Expected: exit 0 (agent completes simulated scaffold)
```

### 6️⃣ CI badge

After the merge, the README badge MUST be green on main.

---

### -- File Stub Snippets --

#### package.json

```json
{
  "name": "workflow-mapper",
  "private": true,
  "packageManager": "pnpm@8.15.4",
  "scripts": {
    "dev:api": "pnpm --filter apps/api nodemon src/index.ts",
    "dev:web": "pnpm --filter apps/web vite",
    "build": "turbo run build --parallel",
    "lint": "eslint . --ext .ts,.tsx",
    "test": "turbo run test --parallel"
  },
  "devDependencies": {
    "typescript": "5.4.3",
    "tsup": "7.2.0",
    "turbo": "latest",
    "eslint": "8.56.0",
    "eslint-plugin-prettier": "5.1.3",
    "prettier": "3.2.5",
    "jest": "29.7.0",
    "supertest": "6.4.2",
    "vitest": "1.5.0"
  }
}
```

#### apps/api/src/index.ts

```ts
import express from 'express';
const app = express();
app.get('/health', (_, res) => res.json({ status: 'ok' }));
app.listen(3000, () => console.log('API listening on :3000'));
```

#### Dockerfile

```dockerfile
FROM node:20.11.0-alpine
WORKDIR /app
COPY . .
RUN corepack enable && pnpm install --frozen-lockfile && pnpm build
CMD ["node", "apps/api/dist/index.js"]
```

#### docker-compose.yml

```yaml
version: '3.9'
services:
  api:
    build: .
    ports: [ "3000:3000" ]
    depends_on: [ neo4j ]
  neo4j:
    image: neo4j:5
    environment:
      - NEO4J_AUTH=neo4j/test
    ports: [ "7474:7474" ]
```

#### scripts/m0-acceptance.sh

```bash
#!/usr/bin/env bash
set -euo pipefail
pnpm install
pnpm build
docker compose up -d --wait
curl -fs http://localhost:3000/health | grep '"status":"ok"' >/dev/null
echo "✅  Acceptance tests passed"
# (Agents must grant +x on the script.)
```
