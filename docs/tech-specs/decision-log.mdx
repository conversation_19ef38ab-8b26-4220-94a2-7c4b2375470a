---
title: Architectural Decision Records (ADRs)
description: Log of all significant architectural decisions made during development.
created: 2025-05-25
updated: 2025-05-25
version: 0.1.0
status: Living
tags: [architecture, decisions]
authors: [nitishMehrotra]
---

> **📋 Purpose:** This document tracks all significant architectural decisions made during the WorkflowMapperAgent project development. Each decision includes context, options considered, and rationale.

---

## 🏗️ Decision Format

Each decision follows this structure:
- **ID**: ADR-XXX
- **Date**: YYYY-MM-DD
- **Status**: Proposed | Accepted | Deprecated | Superseded
- **Context**: What situation led to this decision?
- **Decision**: What did we decide?
- **Consequences**: What are the positive/negative outcomes?

---

## 📋 Decision Index

> **📁 Individual ADR Files**: Each architectural decision is documented in a separate file using the [`adr-template.mdx`](./templates/adr-template.mdx) template.

| ID | Title | Status | Date | File |
|----|-------|--------|------|------|
| ADR-001 | Monorepo Structure with pnpm Workspaces | ✅ Accepted | 2025-05-25 | [`adr-001-monorepo.mdx`](./adrs/adr-001-monorepo.mdx) |
| ADR-002 | TypeScript-First Development | ✅ Accepted | 2025-05-25 | [`adr-002-typescript.mdx`](./adrs/adr-002-typescript.mdx) |
| ADR-003 | JSON-LD for Graph Representation | ✅ Accepted | 2025-05-25 | [`adr-003-jsonld.mdx`](./adrs/adr-003-jsonld.mdx) |
| ADR-004 | Express.js for Backend API | ✅ Accepted | 2025-05-25 | [`adr-004-express.mdx`](./adrs/adr-004-express.mdx) |
| ADR-005 | React + Vite for Frontend | ✅ Accepted | 2025-05-25 | [`adr-005-react-vite.mdx`](./adrs/adr-005-react-vite.mdx) |
| ADR-006 | Neo4j for Graph Database | ✅ Accepted | 2025-05-25 | [`adr-006-neo4j.mdx`](./adrs/adr-006-neo4j.mdx) |

### Status Legend
- 🟡 **Proposed**: Under discussion
- ✅ **Accepted**: Approved and implemented
- ❌ **Rejected**: Decided against
- 🔄 **Superseded**: Replaced by newer decision
- ⚠️ **Deprecated**: No longer recommended

---

## 🔄 Decision Process

### Creating a New ADR
1. **Copy template**: Use [`templates/adr-template.mdx`](./templates/adr-template.mdx)
2. **Create file**: Save as `adrs/adr-XXX-short-title.mdx`
3. **Fill details**: Complete all sections with specific information
4. **Set status**: Start with "Proposed"
5. **Add to index**: Update the table above

### Review & Approval
1. **Discussion**: Team review and feedback on the ADR
2. **Decision**: Update status to "Accepted", "Rejected", or "Superseded"
3. **Implementation**: Reference ADR in related code/docs
4. **Maintenance**: Periodic review of decisions for continued relevance

### File Naming Convention
- Format: `adr-XXX-short-title.mdx`
- Examples: `adr-001-monorepo.mdx`, `adr-015-database-choice.mdx`
- Use sequential numbering (ADR-001, ADR-002, etc.)

---

## 🔗 References

- [Architectural Decision Records](https://adr.github.io/)
- [ADR Tools](https://github.com/npryce/adr-tools)
- [When to Write an ADR](https://engineering.atspotify.com/2020/04/14/when-should-i-write-an-architecture-decision-record/)
