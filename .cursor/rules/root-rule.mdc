---
description: description: Core architecture & coding standards for WorkflowMapperAgent monorepo (root rule) alwaysApply: true
globs:
alwaysApply: false
---
# WorkflowMapperAgent – Project Rules

## Version

_v1.1 – Update rule version header when refactoring so Cursor re-indexes._

## Purpose

Provide Cursor's Agent and Cmd‑K AI with persistent, project‑wide guidance so all code generation, refactors, and documentation tasks align with the architecture and milestones of the WorkflowMapperAgent. **This rule is always applied.**

---

## 0. Repo Philosophy

- **Graph‑first backend** – The JSON‑LD graph is the canonical state; every API mutates or queries it—no parallel domain models.
- **Graph schema versioning & observability** – All changes to the graph schema must be versioned and observable for stability and traceability.
- **Dual-mode scan** – The CLI supports both full and incremental scans; contributors should understand why two CLI targets exist (full for cold start, incremental for fast dev/test cycles).
- **Type‑safe monorepo** – Two workspaces (`backend/`, `frontend/`) in strict TypeScript, sharing types in `/shared`.
- **Incremental & observable** – Prefer additive PRs and surface coverage / audit metrics; avoid mass renames.
- **Human‑review friendly** – Emit TODOs or request clarification instead of guessing.

---

## 1. Directory Structure

> **Consideration:** For future scalability, consider a workspace-oriented monorepo (e.g., `apps/web`, `apps/api`, `packages/*` for shared libraries). This keeps shared libs (parser, agent core, UI components) version-aligned and tree-shakable.
>
> **Colocated tests:** Place tests next to their source files for better dev experience and maintainability.

```
/
├─ backend/               # Express + ts‑node backend
│  ├─ src/
│  │   ├─ agent/          # graph builder logic & tools
│  │   ├─ api/            # REST controllers & routes
│  │   └─ index.ts        # app bootstrap
│  ├─ tests/              # jest unit & integration tests
│  └─ tsconfig.json
│
├─ frontend/              # React 18 + Vite SPA
│  ├─ src/
│  │   ├─ components/
│  │   ├─ pages/
│  │   └─ hooks/
│  ├─ tests/              # vitest + RTL tests
│  └─ tsconfig.json
│
├─ shared/                # common TypeScript types & utils
├─ docs/                  # generated markdown docs (git‑ignored)
├─ .cursor/
│   └─ rules/             # additional scoped rules if needed
├─ e2e/                   # end-to-end Playwright tests (recommended to add early)
├─ package.json           # root (pnpm workspace)
└─ README.md
```

> **Cursor behaviour:** When generating code, place files in their correct sub‑folder and update relevant imports/exports. Generate matching unit tests alongside new modules.

---

## 2. Tech Stack & Dependencies

| Layer     | Runtime / Core libs                | Validation / State | Test libs                |
|-----------|------------------------------------|--------------------|--------------------------|
| Backend   | Node 20, Express ^5, tsup or swc (build), node --env-file (runtime) | Zod                | Jest, Supertest          |
| Frontend  | React 18, Vite, React Router v6, Zustand | Zod (client)      | Vitest, React Testing Library |
| Shared    | TypeScript 5.4, eslint + prettier, tsc‑aliases | —                  | —                        |

- **Backend build:** Prefer `tsup` or `swc` for backend builds to keep production images slim (no TypeScript runtime dependency).
- **RPC option:** Consider adding `@trpc/server` to expose typed procedures if you foresee RPC-style internal calls between UI and API.
- **Dependency review:** Regularly review dependencies, use auto-update tooling, and maintain a deprecation/removal policy for security and maintainability.
- Add new deps by editing `package.json` and documenting them in `/docs/dependencies.md`.

---

## 3. Coding Standards

### General
- Use strict TypeScript (`noImplicitAny`, `noUncheckedIndexedAccess`).
- Named exports only; **forbid non-deterministic re-exports** (`export *`). This helps tree-shaking and code-search.
- Functions ≤60 lines; split otherwise.
- Lint with `eslint --max-warnings 0`; format with Prettier.
- **Commit format:** Enforce a conventional commit message format for consistency and automation.
- **Error codes:** Use structured error codes for all thrown errors and API responses.
- **Coverage:** Maintain code coverage targets and report coverage in CI.
- **Accessibility (a11y):** Ensure all UI components meet accessibility standards.

### Backend
- Express routes live in `backend/src/api/routes.ts`; controllers in `backend/src/api/controllers/*`.
- Validation via Zod schemas colocated with DTOs.
- All route handlers return `Result<T, E>` objects:

```ts
// shared/Result.ts
export type Result<T, E extends Error> = { ok: true; data: T } | { ok: false; error: E };
```

- **Result helper:** Place the `Result` type in `/shared/Result.ts` so front- and back-end share the identical discriminated union—no drift.
- Centralised error middleware at `backend/src/api/errorHandler.ts`.

### Frontend
- Functional React components only; prefer hooks.
- Components live under `frontend/src/components` with `.tsx` extension and colocated CSS Modules.
- Use React Query for async data; Zustand for client state.

### Shared
- Re‑export cross‑cutting interfaces from `shared/index.ts` to avoid deep imports.

---

## 4. Milestones & Branching

| ID | Summary                  | Success metric                                 |
|----|--------------------------|------------------------------------------------|
| M0 | Monorepo skeleton & CI   | `pnpm test` passes < 60 s; CI pipeline green on main; Docker image builds |
| M1 | Static graph builder     | CLI `pnpm run build-graph` outputs JSON‑LD graph |
| M2 | Incremental diff mode    | Graph update < 1 s for 3‑file change           |
| M3 | Spec generator API       | `GET /api/specs` returns valid OpenAPI YAML    |
| M4 | Docs dashboard UI        | Frontend `/docs` shows graph + docs            |
| M5 | Code translation service | `POST /api/translate` returns compilable Go    |

- Create branches as `m{n}-{short-task}` (e.g. `m1-parser`).
- Run `pnpm test --recursive` and `eslint` before each commit.
- **Release process:** Document the release process and use feature flags to enable/disable features for agility and predictability.

---

## 5. Cursor‑specific Guidance

- This rule is always applied; reference via @Cursor Rules if context is lost.
- Use `@code`, `@file`, `@folder` symbols to provide precise context.
- If any rule here blocks generation, Cursor must ask for clarification rather than hallucinate.
- **Rule change process:** Define a process for proposing and approving rule changes.
- **Rule linter:** Use a rule linter to ensure rule compliance and governance.
- **When refactoring, update rule version header at top so Cursor re-indexes.**

---

## 6. General Practices

- **Onboarding:** Maintain up-to-date onboarding documentation for new contributors.
- **Security:** Document and enforce security practices for code, dependencies, and infrastructure.
- **Code ownership:** Map code ownership for all major modules to ensure accountability and quality.

---

## Quick Checklist

If any answer is no, request clarification.

---

_End of rule file_